import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigModuleOptions } from "@nestjs/config";

import { AppConfigService } from "./app-config.service";
import { validate } from "./validation";
import { environment } from "../../environments/environment";

const configOptions: ConfigModuleOptions = {
  isGlobal: true,
  validate,
  cache: true,
  envFilePath: environment.production ? "apps/api/.prod.secrets" : "apps/api/.local.secrets",
};

@Module({
  imports: [ConfigModule.forRoot(configOptions)],
  providers: [AppConfigService],
  exports: [AppConfigService],
})
export class AppConfigModule {}
